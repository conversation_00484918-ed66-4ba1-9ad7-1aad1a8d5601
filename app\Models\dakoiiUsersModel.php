<?php

namespace App\Models;

use CodeIgniter\Model;

class DakoiiUsersModel extends Model
{
    protected $table      = 'dakoii_users';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'name',
        'username',
        'password',
        'role',
        'is_active'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[255]',
        'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username,id,{id}]',
        'password' => 'required|min_length[6]',
        'role' => 'required|in_list[user,moderator,admin,super_admin]',
        'is_active' => 'in_list[0,1]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Name is required',
            'min_length' => 'Name must be at least 3 characters long',
            'max_length' => 'Name cannot exceed 255 characters'
        ],
        'username' => [
            'required' => 'Username is required',
            'min_length' => 'Username must be at least 3 characters long',
            'max_length' => 'Username cannot exceed 255 characters',
            'is_unique' => 'Username already exists'
        ],
        'password' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 6 characters long'
        ],
        'role' => [
            'required' => 'Role is required',
            'in_list' => 'Invalid role selected'
        ]
    ];

    protected $skipValidation = false;

    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Authenticate Dakoii user for login
     */
    public function authenticateDakoiiUser($username, $password)
    {
        // Get user by username
        $user = $this->where('username', $username)
                    ->where('is_active', 1)
                    ->first();

        if (!$user) {
            return false;
        }

        // Verify password
        if (password_verify($password, $user['password'])) {
            return $user;
        }

        return false;
    }

    /**
     * Get Dakoii user statistics for dashboard
     */
    public function getDakoiiUserStats()
    {
        $stats = [
            'total_users' => $this->countAll(),
            'active_users' => $this->where('is_active', 1)->countAllResults(false),
            'inactive_users' => $this->where('is_active', 0)->countAllResults(false),
            'admin_users' => $this->where('role', 'admin')->countAllResults(false),
            'moderator_users' => $this->where('role', 'moderator')->countAllResults(false),
            'regular_users' => $this->where('role', 'user')->countAllResults(false)
        ];

        return $stats;
    }

    /**
     * Get users by role
     */
    public function getDakoiiUsersByRole($role)
    {
        return $this->where('role', $role)
                   ->where('is_active', 1)
                   ->findAll();
    }

    /**
     * Update Dakoii user with validation
     */
    public function updateDakoiiUser($id, $data)
    {
        // Remove password from validation if it's empty
        if (isset($data['password']) && empty($data['password'])) {
            unset($data['password']);
        }

        // Set validation rules for update
        $rules = $this->validationRules;
        if (!isset($data['password'])) {
            unset($rules['password']);
        }

        $this->setValidationRules($rules);

        return $this->update($id, $data);
    }

    /**
     * Create new Dakoii user
     */
    public function createDakoiiUser($data)
    {
        return $this->insert($data);
    }

    /**
     * Get user by username for authentication
     */
    public function getUserByUsername($username)
    {
        return $this->where('username', $username)
                   ->where('is_active', 1)
                   ->first();
    }

    /**
     * Verify user password
     */
    public function verifyPassword($password, $hashedPassword)
    {
        return password_verify($password, $hashedPassword);
    }

    /**
     * Deactivate user (soft delete alternative)
     */
    public function deactivateUser($id)
    {
        return $this->update($id, ['is_active' => 0]);
    }

    /**
     * Deactivate Dakoii user (used by DakoiiUsers controller)
     */
    public function deactivateDakoiiUser($id)
    {
        return $this->update($id, ['is_active' => 0]);
    }

    /**
     * Activate user
     */
    public function activateUser($id)
    {
        return $this->update($id, ['is_active' => 1]);
    }

    /**
     * Get active users only
     */
    public function getActiveUsers()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get users with pagination
     */
    public function getUsersPaginated($perPage = 10, $page = 1)
    {
        return $this->paginate($perPage, 'default', $page);
    }

    /**
     * Search users by name or username
     */
    public function searchUsers($searchTerm)
    {
        return $this->groupStart()
                   ->like('name', $searchTerm)
                   ->orLike('username', $searchTerm)
                   ->groupEnd()
                   ->findAll();
    }
}
