<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class DakoiiData extends BaseController
{
    public $session;
    public $cropsModel;
    public $fertilizersModel;
    public $pesticidesModel;
    public $infectionsModel;
    public $livestockModel;
    public $educationModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        
        // Initialize models
        $this->cropsModel = new \App\Models\CropsModel();
        $this->fertilizersModel = new \App\Models\FertilizersModel();
        $this->pesticidesModel = new \App\Models\PesticidesModel();
        $this->infectionsModel = new \App\Models\InfectionsModel();
        $this->livestockModel = new \App\Models\LivestockModel();
        $this->educationModel = new \App\Models\EducationModel();
    }

    /**
     * Display data management dashboard
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Data Management";
        $data['menu'] = "data";
        
        // Get all data for overview
        $data['crops'] = $this->cropsModel->findAll();
        $data['fertilizers'] = $this->fertilizersModel->findAll();
        $data['pesticides'] = $this->pesticidesModel->findAll();
        $data['infections'] = $this->infectionsModel->findAll();
        $data['livestock'] = $this->livestockModel->findAll();
        $data['education'] = $this->educationModel->findAll();

        return view('dakoii/dakoii_data_index', $data);
    }

    // ==============================================
    // CROPS RESOURCE CONTROLLER METHODS
    // ==============================================

    /**
     * Display crops listing (GET /crops)
     */
    public function index_crops()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Crops Management";
        $data['menu'] = "data";
        $data['crops'] = $this->cropsModel->findAll();

        return view('dakoii/dakoii_data_crops', $data);
    }

    /**
     * Show form to create new crop (GET /crops/create)
     */
    public function create_crop()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New Crop";
        $data['menu'] = "data";

        return view('dakoii/dakoii_data_crops_create', $data);
    }

    /**
     * Store new crop (POST /crops)
     */
    public function store_crop()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Validate input
        $validation = \Config\Services::validation();
        $validation->setRules([
            'crop_name' => 'required|min_length[2]|max_length[100]',
            'crop_color_code' => 'permit_empty|regex_match[/^#[0-9A-Fa-f]{6}$/]',
            'remarks' => 'permit_empty|max_length[500]',
            'crop_icon' => 'permit_empty|uploaded[crop_icon]|is_image[crop_icon]|max_size[crop_icon,2048]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $validation->getErrors()));
            return redirect()->back()->withInput();
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('crop_icon');
            $iconPath = '';

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $iconPath = 'public/uploads/icons/' . $newName;
            }

            $data = [
                'crop_name' => trim($this->request->getPost('crop_name')),
                'crop_color_code' => $this->request->getPost('crop_color_code') ?: '#198754',
                'crop_icon' => $iconPath,
                'remarks' => trim($this->request->getPost('remarks')),
                'created_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($this->cropsModel->insert($data)) {
                session()->setFlashdata('success', 'Crop added successfully');
                return redirect()->to('dakoii/data/crops');
            } else {
                session()->setFlashdata('error', 'Failed to add crop: ' . implode(', ', $this->cropsModel->errors()));
                return redirect()->back()->withInput();
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in store_crop: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while adding the crop: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show a specific crop (GET /crops/{id})
     */
    public function show_crop($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        if (!is_numeric($id) || $id <= 0) {
            session()->setFlashdata('error', 'Invalid crop ID');
            return redirect()->to('dakoii/data/crops');
        }

        $crop = $this->cropsModel->find($id);
        if (!$crop) {
            session()->setFlashdata('error', 'Crop not found');
            return redirect()->to('dakoii/data/crops');
        }

        $data['title'] = "View Crop: " . $crop['crop_name'];
        $data['menu'] = "data";
        $data['crop'] = $crop;

        return view('dakoii/dakoii_data_crops_show', $data);
    }

    /**
     * Show form to edit crop (GET /crops/{id}/edit)
     */
    public function edit_crop($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        if (!is_numeric($id) || $id <= 0) {
            session()->setFlashdata('error', 'Invalid crop ID');
            return redirect()->to('dakoii/data/crops');
        }

        $crop = $this->cropsModel->find($id);
        if (!$crop) {
            session()->setFlashdata('error', 'Crop not found');
            return redirect()->to('dakoii/data/crops');
        }

        $data['title'] = "Edit Crop: " . $crop['crop_name'];
        $data['menu'] = "data";
        $data['crop'] = $crop;

        return view('dakoii/dakoii_data_crops_edit', $data);
    }

    /**
     * Update crop via PUT/PATCH (PUT/PATCH /crops/{id})
     */
    public function update_crop($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        if (!is_numeric($id) || $id <= 0) {
            session()->setFlashdata('error', 'Invalid crop ID');
            return redirect()->to('dakoii/data/crops');
        }

        $existingCrop = $this->cropsModel->find($id);
        if (!$existingCrop) {
            session()->setFlashdata('error', 'Crop not found');
            return redirect()->to('dakoii/data/crops');
        }

        // Validate input
        $validation = \Config\Services::validation();
        $validation->setRules([
            'crop_name' => 'required|min_length[2]|max_length[100]',
            'crop_color_code' => 'permit_empty|regex_match[/^#[0-9A-Fa-f]{6}$/]',
            'remarks' => 'permit_empty|max_length[500]',
            'crop_icon' => 'permit_empty|uploaded[crop_icon]|is_image[crop_icon]|max_size[crop_icon,2048]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $validation->getErrors()));
            return redirect()->back()->withInput();
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('crop_icon');
            $data = [
                'crop_name' => trim($this->request->getPost('crop_name')),
                'crop_color_code' => $this->request->getPost('crop_color_code') ?: '#198754',
                'remarks' => trim($this->request->getPost('remarks')),
                'updated_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                // Delete old icon if exists
                if (!empty($existingCrop['crop_icon'])) {
                    $oldIconPath = ROOTPATH . $existingCrop['crop_icon'];
                    if (file_exists($oldIconPath)) {
                        unlink($oldIconPath);
                    }
                }

                // Save new icon
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $data['crop_icon'] = 'public/uploads/icons/' . $newName;
            }

            if ($this->cropsModel->update($id, $data)) {
                session()->setFlashdata('success', 'Crop updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update crop: ' . implode(', ', $this->cropsModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in update_crop: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while updating the crop: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/crops');
    }

    /**
     * Update crop via form submission (POST /crops/{id})
     * This handles forms that use method spoofing
     */
    public function update_crop_form($id)
    {
        return $this->update_crop($id);
    }

    /**
     * Delete crop (DELETE /crops/{id})
     */
    public function destroy_crop($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        if (!is_numeric($id) || $id <= 0) {
            session()->setFlashdata('error', 'Invalid crop ID');
            return redirect()->to('dakoii/data/crops');
        }

        try {
            $crop = $this->cropsModel->find($id);
            if (!$crop) {
                session()->setFlashdata('error', 'Crop not found');
                return redirect()->to('dakoii/data/crops');
            }

            // Delete icon file if exists
            if (!empty($crop['crop_icon'])) {
                $iconPath = ROOTPATH . $crop['crop_icon'];
                if (file_exists($iconPath)) {
                    unlink($iconPath);
                }
            }

            // Delete the crop
            if ($this->cropsModel->delete($id)) {
                session()->setFlashdata('success', 'Crop deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete crop: ' . implode(', ', $this->cropsModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in destroy_crop: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while deleting the crop: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/crops');
    }

    /**
     * Delete crop via form submission (POST /crops/{id}/delete)
     */
    public function destroy_crop_form($id)
    {
        return $this->destroy_crop($id);
    }

    // ==============================================
    // FERTILIZERS RESOURCE CONTROLLER METHODS
    // ==============================================

    /**
     * Display fertilizers listing (GET /fertilizers)
     */
    public function fertilizers()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Fertilizers Management";
        $data['menu'] = "data";
        $data['fertilizers'] = $this->fertilizersModel->findAll();

        return view('dakoii/dakoii_data_fertilizers', $data);
    }

    /**
     * Show form to create new fertilizer (GET /fertilizers/create)
     */
    public function create_fertilizer()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New Fertilizer";
        $data['menu'] = "data";

        return view('dakoii/dakoii_data_fertilizers_create', $data);
    }

    /**
     * Store new fertilizer (POST /fertilizers/store)
     */
    public function storeFertilizer()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $iconPath = '';

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $iconPath = 'uploads/icons/' . $newName;
            }

            $data = [
                'name' => trim($this->request->getPost('name')),
                'icon' => $iconPath,
                'color_code' => $this->request->getPost('color_code') ?: '#198754',
                'remarks' => trim($this->request->getPost('remarks')),
                'created_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($this->fertilizersModel->insert($data)) {
                session()->setFlashdata('success', 'Fertilizer added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add fertilizer: ' . implode(', ', $this->fertilizersModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in storeFertilizer: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while adding the fertilizer: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/fertilizers');
    }

    /**
     * Show a specific fertilizer (GET /fertilizers/{id})
     */
    public function show_fertilizer($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $fertilizer = $this->fertilizersModel->find($id);
        if (!$fertilizer) {
            session()->setFlashdata('error', 'Fertilizer not found');
            return redirect()->to('dakoii/data/fertilizers');
        }

        $data['title'] = "View Fertilizer: " . $fertilizer['name'];
        $data['menu'] = "data";
        $data['fertilizer'] = $fertilizer;

        return view('dakoii/dakoii_data_fertilizers_show', $data);
    }

    /**
     * Show form to edit fertilizer (GET /fertilizers/{id}/edit)
     */
    public function edit_fertilizer($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $fertilizer = $this->fertilizersModel->find($id);
        if (!$fertilizer) {
            session()->setFlashdata('error', 'Fertilizer not found');
            return redirect()->to('dakoii/data/fertilizers');
        }

        $data['title'] = "Edit Fertilizer: " . $fertilizer['name'];
        $data['menu'] = "data";
        $data['fertilizer'] = $fertilizer;

        return view('dakoii/dakoii_data_fertilizers_edit', $data);
    }

    /**
     * Update fertilizer via PUT/PATCH (PUT/PATCH /fertilizers/{id})
     */
    public function update_fertilizer($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $existingFertilizer = $this->fertilizersModel->find($id);
        if (!$existingFertilizer) {
            session()->setFlashdata('error', 'Fertilizer not found');
            return redirect()->to('dakoii/data/fertilizers');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $data = [
                'name' => trim($this->request->getPost('name')),
                'color_code' => $this->request->getPost('color_code') ?: '#198754',
                'remarks' => trim($this->request->getPost('remarks')),
                'updated_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                // Delete old icon if exists
                if (!empty($existingFertilizer['icon'])) {
                    $oldIconPath = ROOTPATH . 'public/' . $existingFertilizer['icon'];
                    if (file_exists($oldIconPath)) {
                        unlink($oldIconPath);
                    }
                }

                // Save new icon
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $data['icon'] = 'uploads/icons/' . $newName;
            }

            if ($this->fertilizersModel->update($id, $data)) {
                session()->setFlashdata('success', 'Fertilizer updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update fertilizer: ' . implode(', ', $this->fertilizersModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in updateFertilizer: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while updating the fertilizer: ' . $e->getMessage());
        }
        
        return redirect()->to('dakoii/data/fertilizers');
    }

    /**
     * Update fertilizer via form submission (POST /fertilizers/update/{id})
     */
    public function updateFertilizer($id)
    {
        return $this->update_fertilizer($id);
    }

    /**
     * Delete fertilizer (DELETE /fertilizers/{id})
     */
    public function destroy_fertilizer($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        try {
            $fertilizer = $this->fertilizersModel->find($id);
            if (!$fertilizer) {
                session()->setFlashdata('error', 'Fertilizer not found');
                return redirect()->to('dakoii/data/fertilizers');
            }

            // Delete icon file if exists
            if (!empty($fertilizer['icon'])) {
                $iconPath = ROOTPATH . 'public/' . $fertilizer['icon'];
                if (file_exists($iconPath)) {
                    unlink($iconPath);
                }
            }

            // Delete the fertilizer
            if ($this->fertilizersModel->delete($id)) {
                session()->setFlashdata('success', 'Fertilizer deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete fertilizer: ' . implode(', ', $this->fertilizersModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in destroy_fertilizer: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while deleting the fertilizer: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/fertilizers');
    }

    /**
     * Delete fertilizer via form submission (POST /fertilizers/delete/{id})
     */
    public function deleteFertilizer($id)
    {
        return $this->destroy_fertilizer($id);
    }

    // ==============================================
    // PESTICIDES RESOURCE CONTROLLER METHODS
    // ==============================================

    /**
     * Display pesticides management
     */
    public function pesticides()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Pesticides Management";
        $data['menu'] = "data";
        $data['pesticides'] = $this->pesticidesModel->findAll();

        return view('dakoii/dakoii_data_pesticides', $data);
    }

    /**
     * Store new pesticide
     */
    public function storePesticide()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/pesticides');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $iconPath = '';

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $iconPath = 'uploads/icons/' . $newName;
            }

            $data = [
                'name' => trim($this->request->getPost('name')),
                'icon' => $iconPath,
                'color_code' => $this->request->getPost('color_code') ?: '#ffc107',
                'remarks' => trim($this->request->getPost('remarks')),
                'created_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($this->pesticidesModel->insert($data)) {
                session()->setFlashdata('success', 'Pesticide added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add pesticide: ' . implode(', ', $this->pesticidesModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in storePesticide: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while adding the pesticide: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/pesticides');
    }

    /**
     * Update pesticide
     */
    public function updatePesticide($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/pesticides');
        }

        $existingPesticide = $this->pesticidesModel->find($id);
        if (!$existingPesticide) {
            session()->setFlashdata('error', 'Pesticide not found');
            return redirect()->to('dakoii/data/pesticides');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $data = [
                'name' => trim($this->request->getPost('name')),
                'color_code' => $this->request->getPost('color_code') ?: '#ffc107',
                'remarks' => trim($this->request->getPost('remarks')),
                'updated_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                // Delete old icon if exists
                if (!empty($existingPesticide['icon'])) {
                    $oldIconPath = ROOTPATH . 'public/' . $existingPesticide['icon'];
                    if (file_exists($oldIconPath)) {
                        unlink($oldIconPath);
                    }
                }

                // Save new icon
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $data['icon'] = 'uploads/icons/' . $newName;
            }

            if ($this->pesticidesModel->update($id, $data)) {
                session()->setFlashdata('success', 'Pesticide updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update pesticide: ' . implode(', ', $this->pesticidesModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in updatePesticide: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while updating the pesticide: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/pesticides');
    }

    /**
     * Delete pesticide
     */
    public function deletePesticide($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        try {
            $pesticide = $this->pesticidesModel->find($id);
            if (!$pesticide) {
                session()->setFlashdata('error', 'Pesticide not found');
                return redirect()->to('dakoii/data/pesticides');
            }

            // Delete icon file if exists
            if (!empty($pesticide['icon'])) {
                $iconPath = ROOTPATH . 'public/' . $pesticide['icon'];
                if (file_exists($iconPath)) {
                    unlink($iconPath);
                }
            }

            // Delete the pesticide
            if ($this->pesticidesModel->delete($id)) {
                session()->setFlashdata('success', 'Pesticide deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete pesticide: ' . implode(', ', $this->pesticidesModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in deletePesticide: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while deleting the pesticide: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/pesticides');
    }

    // ==============================================
    // INFECTIONS RESOURCE CONTROLLER METHODS
    // ==============================================

    /**
     * Display infections management
     */
    public function infections()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Infections Management";
        $data['menu'] = "data";
        $data['infections'] = $this->infectionsModel->findAll();

        return view('dakoii/dakoii_data_infections', $data);
    }

    /**
     * Store new infection
     */
    public function storeInfection()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/infections');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $iconPath = '';

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $iconPath = 'uploads/icons/' . $newName;
            }

            $data = [
                'name' => trim($this->request->getPost('name')),
                'icon' => $iconPath,
                'color_code' => $this->request->getPost('color_code') ?: '#dc3545',
                'remarks' => trim($this->request->getPost('remarks')),
                'created_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($this->infectionsModel->insert($data)) {
                session()->setFlashdata('success', 'Infection added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add infection: ' . implode(', ', $this->infectionsModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in storeInfection: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while adding the infection: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/infections');
    }

    /**
     * Update infection
     */
    public function updateInfection($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/infections');
        }

        $existingInfection = $this->infectionsModel->find($id);
        if (!$existingInfection) {
            session()->setFlashdata('error', 'Infection not found');
            return redirect()->to('dakoii/data/infections');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $data = [
                'name' => trim($this->request->getPost('name')),
                'color_code' => $this->request->getPost('color_code') ?: '#dc3545',
                'remarks' => trim($this->request->getPost('remarks')),
                'updated_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                // Delete old icon if exists
                if (!empty($existingInfection['icon'])) {
                    $oldIconPath = ROOTPATH . 'public/' . $existingInfection['icon'];
                    if (file_exists($oldIconPath)) {
                        unlink($oldIconPath);
                    }
                }

                // Save new icon
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $data['icon'] = 'uploads/icons/' . $newName;
            }

            if ($this->infectionsModel->update($id, $data)) {
                session()->setFlashdata('success', 'Infection updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update infection: ' . implode(', ', $this->infectionsModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in updateInfection: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while updating the infection: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/infections');
    }

    /**
     * Delete infection
     */
    public function deleteInfection($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        try {
            $infection = $this->infectionsModel->find($id);
            if (!$infection) {
                session()->setFlashdata('error', 'Infection not found');
                return redirect()->to('dakoii/data/infections');
            }

            // Delete icon file if exists
            if (!empty($infection['icon'])) {
                $iconPath = ROOTPATH . 'public/' . $infection['icon'];
                if (file_exists($iconPath)) {
                    unlink($iconPath);
                }
            }

            // Delete the infection
            if ($this->infectionsModel->delete($id)) {
                session()->setFlashdata('success', 'Infection deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete infection: ' . implode(', ', $this->infectionsModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in deleteInfection: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while deleting the infection: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/infections');
    }

    // ==============================================
    // LIVESTOCK RESOURCE CONTROLLER METHODS
    // ==============================================

    /**
     * Display livestock management
     */
    public function livestock()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Livestock Management";
        $data['menu'] = "data";
        $data['livestock'] = $this->livestockModel->findAll();

        return view('dakoii/dakoii_data_livestock', $data);
    }

    /**
     * Store new livestock
     */
    public function storeLivestock()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/livestock');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $iconPath = '';

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $iconPath = 'uploads/icons/' . $newName;
            }

            $data = [
                'livestock_name' => trim($this->request->getPost('livestock_name')),
                'livestock_icon' => $iconPath,
                'livestock_color_code' => $this->request->getPost('livestock_color_code') ?: '#6f42c1',
                'remarks' => trim($this->request->getPost('remarks')),
                'created_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($this->livestockModel->insert($data)) {
                session()->setFlashdata('success', 'Livestock added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add livestock: ' . implode(', ', $this->livestockModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in storeLivestock: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while adding the livestock: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/livestock');
    }

    /**
     * Update livestock
     */
    public function updateLivestock($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/livestock');
        }

        $existingLivestock = $this->livestockModel->find($id);
        if (!$existingLivestock) {
            session()->setFlashdata('error', 'Livestock not found');
            return redirect()->to('dakoii/data/livestock');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $data = [
                'livestock_name' => trim($this->request->getPost('livestock_name')),
                'livestock_color_code' => $this->request->getPost('livestock_color_code') ?: '#6f42c1',
                'remarks' => trim($this->request->getPost('remarks')),
                'updated_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                // Delete old icon if exists
                if (!empty($existingLivestock['livestock_icon'])) {
                    $oldIconPath = ROOTPATH . 'public/' . $existingLivestock['livestock_icon'];
                    if (file_exists($oldIconPath)) {
                        unlink($oldIconPath);
                    }
                }

                // Save new icon
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $data['livestock_icon'] = 'uploads/icons/' . $newName;
            }

            if ($this->livestockModel->update($id, $data)) {
                session()->setFlashdata('success', 'Livestock updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update livestock: ' . implode(', ', $this->livestockModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in updateLivestock: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while updating the livestock: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/livestock');
    }

    /**
     * Delete livestock
     */
    public function deleteLivestock($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        try {
            $livestock = $this->livestockModel->find($id);
            if (!$livestock) {
                session()->setFlashdata('error', 'Livestock not found');
                return redirect()->to('dakoii/data/livestock');
            }

            // Delete icon file if exists
            if (!empty($livestock['livestock_icon'])) {
                $iconPath = ROOTPATH . 'public/' . $livestock['livestock_icon'];
                if (file_exists($iconPath)) {
                    unlink($iconPath);
                }
            }

            // Delete the livestock
            if ($this->livestockModel->delete($id)) {
                session()->setFlashdata('success', 'Livestock deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete livestock: ' . implode(', ', $this->livestockModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in deleteLivestock: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while deleting the livestock: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/livestock');
    }

    // ==============================================
    // EDUCATION RESOURCE CONTROLLER METHODS
    // ==============================================

    /**
     * Display education management
     */
    public function education()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Education Management";
        $data['menu'] = "data";
        $data['education'] = $this->educationModel->findAll();

        return view('dakoii/dakoii_data_education', $data);
    }

    /**
     * Store new education
     */
    public function storeEducation()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/education');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $iconPath = '';

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $iconPath = 'uploads/icons/' . $newName;
            }

            $data = [
                'name' => trim($this->request->getPost('name')),
                'icon' => $iconPath,
                'color_code' => $this->request->getPost('color_code') ?: '#17a2b8',
                'remarks' => trim($this->request->getPost('remarks')),
                'created_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($this->educationModel->insert($data)) {
                session()->setFlashdata('success', 'Education added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add education: ' . implode(', ', $this->educationModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in storeEducation: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while adding the education: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/education');
    }

    /**
     * Update education
     */
    public function updateEducation($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/education');
        }

        $existingEducation = $this->educationModel->find($id);
        if (!$existingEducation) {
            session()->setFlashdata('error', 'Education not found');
            return redirect()->to('dakoii/data/education');
        }

        try {
            // Handle file upload
            $icon = $this->request->getFile('icon');
            $data = [
                'name' => trim($this->request->getPost('name')),
                'color_code' => $this->request->getPost('color_code') ?: '#17a2b8',
                'remarks' => trim($this->request->getPost('remarks')),
                'updated_by' => session()->get('dakoii_user_id') ?: 1
            ];

            if ($icon && $icon->isValid() && !$icon->hasMoved()) {
                // Delete old icon if exists
                if (!empty($existingEducation['icon'])) {
                    $oldIconPath = ROOTPATH . 'public/' . $existingEducation['icon'];
                    if (file_exists($oldIconPath)) {
                        unlink($oldIconPath);
                    }
                }

                // Save new icon
                $newName = $icon->getRandomName();
                $uploadPath = ROOTPATH . 'public/uploads/icons/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                $icon->move($uploadPath, $newName);
                $data['icon'] = 'uploads/icons/' . $newName;
            }

            if ($this->educationModel->update($id, $data)) {
                session()->setFlashdata('success', 'Education updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update education: ' . implode(', ', $this->educationModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in updateEducation: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while updating the education: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/education');
    }

    /**
     * Delete education
     */
    public function deleteEducation($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        try {
            $education = $this->educationModel->find($id);
            if (!$education) {
                session()->setFlashdata('error', 'Education not found');
                return redirect()->to('dakoii/data/education');
            }

            // Delete icon file if exists
            if (!empty($education['icon'])) {
                $iconPath = ROOTPATH . 'public/' . $education['icon'];
                if (file_exists($iconPath)) {
                    unlink($iconPath);
                }
            }

            // Delete the education
            if ($this->educationModel->delete($id)) {
                session()->setFlashdata('success', 'Education deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete education: ' . implode(', ', $this->educationModel->errors()));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in deleteEducation: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while deleting the education: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/data/education');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }
}